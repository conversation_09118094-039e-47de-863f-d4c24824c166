const CancellationReason = require('../models/CancellationReason');

class CancellationReasonsService {
  static async getAllCancellationReasons(req, res) {
    try {
      // Get all cancellation reasons from the database
      const reasons = await CancellationReason.findAll();
      return reasons;
    } catch (error) {
      console.error('Service error fetching cancellation reasons:', error);
      throw error;
    }
  }
};

module.exports = CancellationReasonsService;