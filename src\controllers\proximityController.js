const { calculateDistance, validateCoordinates } = require('../utils/geoUtils');
const { generateSignedUrl } = require('../utils/s3Utils');
const db = require('../config/database');

const findNearbyNurses = async (req, res) => {
    let connection;
    
    try {
        // Extract authenticated user information from req.user (set by authenticateToken middleware)
        const authenticatedUser = req.user;
        const { customerId, radius, addressId } = req.query;

        // Validate radius
        const validRadii = [2, 3, 5, 7];
        if (!validRadii.includes(Number(radius))) {
            return res.status(400).json({
                error: 'Invalid radius. Please use 2, 3, 5, or 7 kilometers.'
            });
        }

        // Validate required parameters
        if (!customerId || !addressId) {
            return res.status(400).json({
                error: 'customerId and addressId are required'
            });
        }

        // Get a connection from the pool
        connection = await db.pool.getConnection();

        // Get customer location - ensure the address belongs to the authenticated user
        const [customer] = await connection.execute(
            'SELECT latitude, longitude, address FROM test_customer_api.addresses WHERE id = ? AND user_id = ?',
            [addressId, customerId]
        );

        if (!customer || !customer.length) {
            return res.status(404).json({
                error: 'Customer address not found or does not belong to authenticated user'
            });
        }

        const { latitude: customerLat, longitude: customerLon, address: customerAdd } = customer[0];

        if (!validateCoordinates(customerLat, customerLon)) {
            return res.status(400).json({
                error: 'Invalid customer coordinates'
            });
        }

        // Call stored procedure with proper connection handling
        const [nursesResult] = await connection.execute('CALL findNurseNearByDefault()');
        
        // Handle the stored procedure result properly
        // Stored procedures return an array where the first element contains the result set
        const nurses = Array.isArray(nursesResult[0]) ? nursesResult[0] : nursesResult;

        // Calculate distances and filter nurses within radius
        const filteredNurses = await Promise.all(
            nurses
                .map(async nurse => {
                    const distance = calculateDistance(
                        customerLat,
                        customerLon,
                        nurse.latitude,
                        nurse.longitude
                    );

                    // Safely parse availability slots
                    let availabilitySlots;
                    try {
                        availabilitySlots = nurse.availability_slots
                            ? JSON.parse(nurse.availability_slots)
                            : [];
                    } catch (parseError) {
                        console.warn('Failed to parse availability_slots for nurse:', nurse.id);
                        availabilitySlots = [];
                    }

                    // Generate signed URL for profile image if s3_key exists
                    let profileImageSignedUrl = null;
                    if (nurse.s3_key) {
                        try {
                            profileImageSignedUrl = await generateSignedUrl(nurse.s3_key);
                        } catch (error) {
                            console.warn(`Failed to generate signed URL for nurse ${nurse.id}:`, error);
                            profileImageSignedUrl = null;
                        }
                    }

                    return {
                        ...nurse,
                        distance: parseFloat(distance.toFixed(2)),
                        availability_slots: availabilitySlots,
                        profile_image_signed_url: profileImageSignedUrl
                    };
                })
        );

        // Filter nurses within radius and sort by distance
        const nearbyNurses = filteredNurses
            .filter(nurse => nurse.distance <= radius)
            .sort((a, b) => a.distance - b.distance);

        return res.json({
            customerId,
            customerAddress: customerAdd,
            customerLat: Number(customerLat),
            customerLon: Number(customerLon),
            radius: Number(radius),
            totalNursesFound: nearbyNurses.length,
            nurses: nearbyNurses
        });

    } catch (error) {
        console.error('Error finding nearby nurses:', error);
        console.error('Authenticated user:', req.user);
        return res.status(500).json({
            error: 'Internal server error',
            ...(process.env.NODE_ENV === 'development' && { details: error.message })
        });
    } finally {
        // Always release the connection back to the pool
        if (connection) {
            try {
                connection.release();
            } catch (releaseError) {
                console.error('Error releasing database connection:', releaseError);
            }
        }
    }
};

module.exports = { findNearbyNurses };