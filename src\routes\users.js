const express = require('express');
const UserController = require('../controllers/userController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');

const router = express.Router();

// Protected endpoints (require authentication)
router.post('/profile', authenticateToken, checkDatabaseConnection, requireUserType, UserController.getProfile);
router.put('/profile', authenticateToken, checkDatabaseConnection, requireUserType, UserController.updateProfile);
router.delete('/profile', authenticateToken, checkDatabaseConnection, requireUserType, UserController.deleteProfile);

// Update user location
router.put('/location', authenticateToken, checkDatabaseConnection, requireUserType, UserController.updateLocation);

// List users with pagination, search, and filters
router.get('/', authenticateToken, checkDatabaseConnection, requireUserType, UserController.listUsers);

// Get user by ID
router.get('/:userId', authenticateToken, checkDatabaseConnection, requireUserType, UserController.getUserById);

module.exports = router;