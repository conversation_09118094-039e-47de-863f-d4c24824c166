const express = require('express');
const router = express.Router();
const { findNearbyNurses } = require('../controllers/proximityController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const proximityController = require('../controllers/proximityController');

router.use(authenticateToken);
router.use(requireUserType);

router.get('/nurses', proximityController.findNearbyNurses);


module.exports = router; 