const express = require("express");
const router = express.Router();
const {
  createBooking,
  updateBookingStatus,
  getAllBookings,
  getBookingById,
  getBookingsByCustomer,
  getBookingsByNurse,
} = require("../controllers/bookingController");
const { authenticateToken, requireUserType } = require('../middleware/auth');
const {
  createBookingValidation,
  updateBookingStatusValidation,
  getBookingByIdValidation,
  getBookingsByCustomerValidation,
  getBookingsByNurseValidation,
  handleValidationErrors,
} = require("../middleware/bookingValidation");

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

router.use(authenticateToken);

// POST /api/bookings/create-booking - Create a new booking
router.post("/create-booking",requireUserType, createBookingValidation, handleValidationErrors, createBooking );

// PUT /api/bookings/:booking_id/status - Update booking status
router.put("/:booking_id/status",requireUserType, updateBookingStatusValidation, handleValidationErrors, updateBookingStatus);

// GET /api/bookings/get-all-bookings - Get all bookings
router.get("/get-all-bookings",requireNurseOrCustomer, getAllBookings);

// GET /api/bookings/:booking_id - Get booking by ID
router.get(
  "/:booking_id",
  getBookingByIdValidation,
  requireNurseOrCustomer,
  handleValidationErrors,
  getBookingById
);

// GET /api/bookings/customer/:customer_cognitoId - Get bookings by customer
router.get(
  "/customer/:customer_cognitoId",
  requireNurseOrCustomer,
  getBookingsByCustomerValidation,
  handleValidationErrors,
  getBookingsByCustomer
);

// GET /api/bookings/nurse/:nurse_cognitoId - Get bookings by nurse
router.get(
  "/nurse/:nurse_cognitoId",
  requireNurseOrCustomer,
  getBookingsByNurseValidation,
  handleValidationErrors,
  getBookingsByNurse
);

module.exports = router;
