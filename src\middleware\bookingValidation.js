const { body, param, validationResult } = require('express-validator');

// Validation for creating a booking
const createBookingValidation = [
  body('nurse_cognitoId')
    .notEmpty()
    .withMessage('Nurse Cognito ID is required')
    .isString()
    .withMessage('Nurse Cognito ID must be a string')
    .isLength({ min: 1, max: 255 })
    .withMessage('Nurse Cognito ID must be between 1 and 255 characters'),
  
  body('customer_given_name')
    .notEmpty()
    .withMessage('Customer given name is required')
    .isString()
    .withMessage('Customer given name must be a string')
    .isLength({ min: 1, max: 255 })
    .withMessage('Customer given name must be between 1 and 255 characters'),
  
  body('customer_booked_location_address')
    .notEmpty()
    .withMessage('Customer booked location address is required')
    .isString()
    .withMessage('Customer booked location address must be a string')
    .isLength({ min: 1, max: 500 })
    .withMessage('Customer booked location address must be between 1 and 500 characters'),
  
  body('customer_booked_location_latitude')
    .notEmpty()
    .withMessage('Customer booked location latitude is required')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Customer booked location latitude must be a valid latitude between -90 and 90'),
  
  body('customer_booked_location_longitude')
    .notEmpty()
    .withMessage('Customer booked location longitude is required')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Customer booked location longitude must be a valid longitude between -180 and 180'),
  
  body('services_selected')
    .notEmpty()
    .withMessage('Services selected is required')
    .isArray({ min: 1 })
    .withMessage('Services selected must be an array with at least one service'),
  
  body('services_selected.*')
    .isString()
    .withMessage('Each service must be a string')
    .isLength({ min: 1, max: 255 })
    .withMessage('Each service must be between 1 and 255 characters'),
  
  body('hourly_fare')
    .notEmpty()
    .withMessage('Hourly fare is required')
    .isFloat({ min: 0 })
    .withMessage('Hourly fare must be a positive number'),
  
  body('booked_date')
    .notEmpty()
    .withMessage('Booked date is required')
    .isISO8601()
    .withMessage('Booked date must be a valid ISO 8601 date (YYYY-MM-DD)')
    .custom((value) => {
      const bookingDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day
      
      if (bookingDate < today) {
        throw new Error('Booked date cannot be in the past');
      }
      return true;
    }),
  
  body('booked_slot')
    .notEmpty()
    .withMessage('Booked slot is required')
    .isString()
    .withMessage('Booked slot must be a string')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Booked slot must be in HH:MM format (e.g., 10:00, 14:30)')
];

// Validation for updating booking status
const updateBookingStatusValidation = [
  param('booking_id')
    .notEmpty()
    .withMessage('Booking ID is required')
    .isInt({ min: 1 })
    .withMessage('Booking ID must be a positive integer'),
  
  body('booking_status')
    .notEmpty()
    .withMessage('Booking status is required')
    .isIn(['Pending', 'Accepted', 'Declined', 'Cancelled', 'Completed'])
    .withMessage('Booking status must be one of: Pending, Accepted, Declined, Cancelled, Completed')
];

// Validation for getting booking by ID
const getBookingByIdValidation = [
  param('booking_id')
    .notEmpty()
    .withMessage('Booking ID is required')
    .isInt({ min: 1 })
    .withMessage('Booking ID must be a positive integer')
];

// Validation for getting bookings by customer
const getBookingsByCustomerValidation = [
  param('customer_cognitoId')
    .notEmpty()
    .withMessage('Customer Cognito ID is required')
    .isString()
    .withMessage('Customer Cognito ID must be a string')
    .isLength({ min: 1, max: 255 })
    .withMessage('Customer Cognito ID must be between 1 and 255 characters')
];

// Validation for getting bookings by nurse
const getBookingsByNurseValidation = [
  param('nurse_cognitoId')
    .notEmpty()
    .withMessage('Nurse Cognito ID is required')
    .isString()
    .withMessage('Nurse Cognito ID must be a string')
    .isLength({ min: 1, max: 255 })
    .withMessage('Nurse Cognito ID must be between 1 and 255 characters')
];

// Error handling middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorDetails = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json({
      error: 'Validation failed',
      details: errorDetails
    });
  }
  next();
};

module.exports = {
  createBookingValidation,
  updateBookingStatusValidation,
  getBookingByIdValidation,
  getBookingsByCustomerValidation,
  getBookingsByNurseValidation,
  handleValidationErrors,
};