version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nurserv-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
      - AWS_REGION=${AWS_REGION}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - COGNITO_APP_CLIENT_ID=${COGNITO_APP_CLIENT_ID}
      - DB_HOST=${DB_HOST}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
    volumes:
      - ./logs:/app/logs
    networks:
      - nurserv-network

networks:
  nurserv-network:
    driver: bridge 