const { pool } = require('../config/database');

class PersonalDetails {
  constructor(data) {   
    this.id = data.id;
    this.user_id = data.user_id;
    this.cognito_id = data.cognito_id;
    this.date_of_birth = data.date_of_birth ? new Date(data.date_of_birth).toLocaleDateString("en-CA") : null;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.phone_number = data.phone_number;
    this.user_name = data.user_name;
    this.given_name = data.given_name;
    this.family_name = data.family_name;
    this.email = data.email;
    this.longitude = data.longitude;
    this.latitude = data.latitude;
    this.address = data.address;
    this.about = data.about;
    this.user_type = data.user_type; // Added for user type tracking
  }

  static async create(detailsData, userType) {
    try {
      // Validate user type
      if (!userType || (userType !== 'customer' && userType !== 'nurse')) {
        throw new Error('Invalid user type');
      }

      // Format date fields properly
      const formattedData = { ...detailsData };
      
      // Format date_of_birth to MySQL date format (YYYY-MM-DD) if it exists
      if (formattedData.date_of_birth) {
        const dateObj = new Date(formattedData.date_of_birth);
        if (!isNaN(dateObj.getTime())) {
          formattedData.date_of_birth = dateObj.toISOString().split('T')[0];
        }
      }
      
      // Get valid columns from the users table
      const [columns] = await pool.query(`SHOW COLUMNS FROM users`);
      const validColumns = columns.map(col => col.Field);

      // Prepare only valid columns and values
      const fields = [];
      const values = [];
      const placeholders = [];
      
      for (const [key, value] of Object.entries(formattedData)) {
        if (validColumns.includes(key)) {
          fields.push(key);
          values.push(value);
          placeholders.push('?');
        }
      }
      
      // Always include required fields
      if (!fields.includes('cognito_id') && formattedData.cognito_id) {
        fields.push('cognito_id');
        values.push(formattedData.cognito_id);
        placeholders.push('?');
      }
      
      if (!fields.includes('created_at')) {
        fields.push('created_at');
        values.push(new Date());
        placeholders.push('?');
      }
      
      if (!fields.includes('updated_at')) {
        fields.push('updated_at');
        values.push(new Date());
        placeholders.push('?');
      }
      
      // Execute the query with only valid columns
      const [result] = await pool.query(
        `INSERT INTO users (${fields.join(', ')})
         VALUES (${placeholders.join(', ')})`,
        values
      );

      return { id: result.insertId, ...detailsData };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create personal details');
    }
  }

  static async findByUserId(userId) {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      const [rows] = await pool.query(
        `SELECT * FROM users WHERE cognito_id = ?`,
        [userId]
      );
      
      return rows.length ? new PersonalDetails(rows[0]) : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch personal details');
    }
  }

  static async personalDetailsUpdate(userId, updateData, userType) {
    try {
      // Validate inputs
      if (!userId) {
        throw new Error('User ID is required');
      }
      
      if (!userType || (userType !== 'customer' && userType !== 'nurse')) {
        throw new Error('Invalid user type');
      }

      // Format date fields properly
      const formattedData = { ...updateData };
      
      if (formattedData.date_of_birth) {
        const dateObj = new Date(formattedData.date_of_birth);
        if (!isNaN(dateObj.getTime())) {
          formattedData.date_of_birth = dateObj.toISOString().split('T')[0];
        }
      }
      
      // Check if record exists
      const existingRecord = await this.findByUserId(userId);
      
      if (!existingRecord || !existingRecord.id) {
        // Create new record if it doesn't exist
        return await this.create({
          cognito_id: userId,
          ...formattedData
        }, userType);
      }

      // Ensure user can only update their own record
      if (existingRecord.cognito_id !== userId) {
        throw new Error('Unauthorized: Cannot update another user\'s record');
      }
      
      // Fields that can be updated in users table
      const userFields = [
        'given_name', 'family_name', 'email', 'phone_number',
        'latitude', 'longitude', 'address', 'city', 'state',
        'country', 'postal_code', 'date_of_birth', 'about'
      ];

      // Prepare updates for users table
      const userUpdates = [];
      const userValues = [];
      
      for (const [key, value] of Object.entries(formattedData)) {
        if (userFields.includes(key)) {
          userUpdates.push(`${key} = ?`);
          userValues.push(value);
        }
      }

      // Start transaction
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        // Update users table if there are changes
        if (userUpdates.length > 0) {
          userValues.push(new Date(), userId);
          
          const [userResult] = await connection.query(
            `UPDATE users 
             SET ${userUpdates.join(', ')}, updated_at = ? 
             WHERE cognito_id = ?`,
            userValues
          );
          
          if (userResult.affectedRows === 0) {
            throw new Error('Failed to update user record');
          }
        }

        // Commit transaction
        await connection.commit();

        // Return updated record
        return await this.findByUserId(userId);
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update personal details');
    }
  }

  static async professionalDetailsUpdate(userId, updateData, userType) {
    try {
      // Only nurses can update professional details
      if (userType !== 'nurse') {
        throw new Error('Unauthorized: Only nurses can update professional details');
      }

      // Use the same logic as personalDetailsUpdate but with professional-specific fields
      return await this.personalDetailsUpdate(userId, updateData, userType);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update professional details');
    }
  }

  static async delete(userId, userType) {
    try {
      // Validate inputs
      if (!userId) {
        throw new Error('User ID is required');
      }
      
      if (!userType || (userType !== 'customer' && userType !== 'nurse')) {
        throw new Error('Invalid user type');
      }

      // Ensure user can only delete their own record
      const existingRecord = await this.findByUserId(userId);
      if (existingRecord && existingRecord.cognito_id !== userId) {
        throw new Error('Unauthorized: Cannot delete another user\'s record');
      }

      const [result] = await pool.query(
        'DELETE FROM users WHERE cognito_id = ?',
        [userId]
      );
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to delete personal details');
    }
  }
}

module.exports = PersonalDetails;