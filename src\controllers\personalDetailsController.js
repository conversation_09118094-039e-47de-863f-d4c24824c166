const PersonalDetailsService = require('../services/personalDetailsService');

class PersonalDetailsController {
  static async createPersonalDetails(req, res) {
    try {
      // Validate user authentication
      if (!req.user || !req.user.sub) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Validate user type if needed for specific operations
      if (req.user.userType !== 'customer' && req.user.userType !== 'nurse') {
        return res.status(403).json({ error: 'Invalid user type' });
      }

      const details = await PersonalDetailsService.upsertPersonalDetails(
        req.user.sub,
        req.body,
        req.user.userType // Pass user type for additional validation
      );

      res.status(200).json({
        message: 'Personal details created successfully',
        details
      });
    } catch (error) {
      console.error('Controller error:', error);
      
      // Handle specific error types
      if (error.message === 'Personal details already exist for this user') {
        res.status(409).json({ error: error.message });
      } else if (error.message === 'Unauthorized access') {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to create personal details' });
      }
    }
  }

  static async getPersonalDetails(req, res) {
    try {
      // Validate user authentication
      if (!req.user || !req.user.sub) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const details = await PersonalDetailsService.getPersonalDetails(
        req.user.sub,
        req.user.userType
      );

      res.json({ details });
    } catch (error) {
      console.error('Controller error:', error);
      
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Unauthorized access') {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to get personal details' });
      }
    }
  }

  static async updatePersonalDetails(req, res) {
    try {
      // Validate user authentication
      if (!req.user || !req.user.sub) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Prevent users from updating other users' data
      const details = await PersonalDetailsService.updatePersonalDetails(
        req.user.sub,
        req.body,
        req.user.userType
      );

      res.json({
        message: 'Personal details updated successfully',
        details
      });
    } catch (error) {
      console.error('Controller error:', error);
      
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Unauthorized access') {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to update personal details' });
      }
    }
  }

  static async deletePersonalDetails(req, res) {
    try {
      // Validate user authentication
      if (!req.user || !req.user.sub) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      const result = await PersonalDetailsService.deletePersonalDetails(
        req.user.sub,
        req.user.userType
      );

      res.json(result);
    } catch (error) {
      console.error('Controller error:', error);
      
      if (error.message === 'Personal details not found') {
        res.status(404).json({ error: error.message });
      } else if (error.message === 'Unauthorized access') {
        res.status(403).json({ error: error.message });
      } else {
        res.status(500).json({ error: 'Failed to delete personal details' });
      }
    }
  }
}

module.exports = PersonalDetailsController;