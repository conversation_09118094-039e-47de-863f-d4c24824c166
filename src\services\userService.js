const db = require('../config/database');
const pool = db.pool;

class UserService {
  static async findByUsername(username) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE username = ?', [username]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by username');
    }
  }

  static async findByEmail(email) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE email = ?', [email]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by email');
    }
  }

  static async findByPhoneNumber(phoneNumber) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE phone_number = ?', [phoneNumber]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by phone number');
    }
  }

  static async create(userData) {
    try {
      const now = new Date();
      const [result] = await pool.query(
        'INSERT INTO users (cognito_id, username, email, name, phone_number, middle_name, family_name, given_name, created_at, updated_at, is_confirmed) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          userData.cognito_id,
          userData.username,
          userData.email,
          userData.name || null,
          userData.phone_number || null,
          userData.middle_name || null,
          userData.family_name || null,
          userData.given_name || null,
          now,
          now,
          userData.is_confirmed || false
        ]
      );

      return { id: result.insertId, ...userData };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to create user in database');
    }
  }

  static async updateConfirmationStatus(username, status) {
    try {
      await pool.query(
        'UPDATE users SET is_confirmed = ?, updated_at = ? WHERE username = ?',
        [status, new Date(), username]
      );
      return true;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update user confirmation status');
    }
  }

  static async updateProfile(username, cognitoId, { given_name, family_name, phone_number }) {
    try {
      // Verify the user exists and the cognitoId matches
      const existingUser = await this.findByUsername(username);
      if (!existingUser) {
        throw new Error('User not found');
      }

      if (existingUser.cognito_id !== cognitoId) {
        throw new Error('Unauthorized: User ID mismatch');
      }

      const [result] = await pool.query(
        'UPDATE users SET given_name = ?, family_name = ?, phone_number = ?, updated_at = ? WHERE username = ? AND cognito_id = ?',
        [given_name, family_name, phone_number, new Date(), username, cognitoId]
      );

      if (result.affectedRows === 0) {
        return null;
      }

      return this.findByUsername(username);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update user profile');
    }
  }

  static async deleteUser(username) {
    try {
      // First get the user's cognito_id since that's what the stored procedure needs
      const getUserQuery = 'SELECT cognito_id FROM users WHERE username = ?';
      const [userRows] = await pool.query(getUserQuery, [username]);

      if (!userRows || userRows.length === 0) {
        throw new Error('User not found');
      }

      const cognitoId = userRows[0].cognito_id;

      // Call the stored procedure with cognito_id
      const [result] = await pool.query('CALL DeleteUserRecords(?)', [cognitoId]);

      return true;
    } catch (error) {
      console.error('Database error:', error);
      // Handle specific stored procedure errors
      if (error.code === 'ER_SIGNAL_EXCEPTION') {
        throw new Error('Failed to delete user records - database constraint error');
      }
      throw new Error('Failed to delete user');
    }
  }

  static async listUsers({
    page = 1,
    limit = 10,
    search,
    email,
    username,
    is_confirmed,
    sort_by = 'created_at',
    sort_order = 'DESC',
    fields,
    requestingUserType,
    requestingUserId
  }) {
    try {
      const offset = (page - 1) * limit;
      const whereConditions = [];
      const queryParams = [];

      // Base search conditions
      if (search) {
        whereConditions.push('(username LIKE ? OR email LIKE ? OR given_name LIKE ? OR family_name LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
      }

      if (email) {
        whereConditions.push('email = ?');
        queryParams.push(email);
      }

      if (username) {
        whereConditions.push('username = ?');
        queryParams.push(username);
      }

      if (is_confirmed !== undefined) {
        whereConditions.push('is_confirmed = ?');
        queryParams.push(is_confirmed === 'true');
      }

      // Authorization-based filtering
      if (requestingUserType === 'customer') {
        // Customers can only see their own profile
        whereConditions.push('cognito_id = ?');
        queryParams.push(requestingUserId);
      }
      // Nurses can see all users (no additional filtering needed)

      const allowedSortFields = ['username', 'email', 'created_at', 'updated_at', 'is_confirmed'];
      const sortBy = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
      const sortOrder = sort_order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

      const whereClause = whereConditions.length > 0
        ? 'WHERE ' + whereConditions.join(' AND ')
        : '';

      // Define fields based on user type
      let selectedFields = 'id, username, email, given_name, family_name, phone_number, created_at, updated_at, is_confirmed';

      if (requestingUserType === 'nurse') {
        // Nurses can see additional fields
        selectedFields += ', cognito_id, latitude, longitude, address, customer_set_location';
      }

      if (fields) {
        const requestedFields = fields.split(',').map(f => f.trim());
        const baseValidFields = ['id', 'username', 'email', 'given_name', 'family_name', 'phone_number', 'created_at', 'updated_at', 'is_confirmed'];
        const nurseOnlyFields = ['cognito_id', 'latitude', 'longitude', 'address', 'customer_set_location'];

        let validFields = baseValidFields;
        if (requestingUserType === 'nurse') {
          validFields = [...baseValidFields, ...nurseOnlyFields];
        }

        const filteredFields = requestedFields.filter(f => validFields.includes(f));
        if (filteredFields.length > 0) {
          selectedFields = filteredFields.join(', ');
        }
      }

      const [countResult] = await pool.query(
        `SELECT COUNT(*) as total FROM users ${whereClause}`,
        queryParams
      );
      const total = countResult[0].total;

      const [rows] = await pool.query(
        `SELECT ${selectedFields}
         FROM users
         ${whereClause}
         ORDER BY ${sortBy} ${sortOrder}
         LIMIT ? OFFSET ?`,
        [...queryParams, limit, offset]
      );

      return {
        users: rows,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        filters: {
          search: search || null,
          email: email || null,
          username: username || null,
          is_confirmed: is_confirmed !== undefined ? is_confirmed === 'true' : null
        },
        sorting: {
          sortBy,
          sortOrder
        },
        userType: requestingUserType
      };
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch users');
    }
  }

  static async findById(userId) {
    try {
      const [rows] = await pool.query('SELECT * FROM users WHERE cognito_id = ?', [userId]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user by ID');
    }
  }

  static async updateLocation(username, cognito_id, given_name, locationData) {
    try {
      // Verify the user exists and the cognito_id matches
      const existingUser = await this.findByUsername(username);
      if (!existingUser) {
        throw new Error('User not found');
      }

      if (existingUser.cognito_id !== cognito_id) {
        throw new Error('Unauthorized: User ID mismatch');
      }

      const { latitude, longitude, address } = locationData;
      const customer_set_location = true;

      // Insert new address record
      const [result] = await pool.query(
        "INSERT INTO addresses (user_id, name, address, created_at, updated_at, longitude, latitude) VALUES (?, ?, ?, CONVERT_TZ(NOW(), 'UTC', '+05:30'), CONVERT_TZ(NOW(), 'UTC', '+05:30'), ?, ?)",
        [cognito_id, given_name, address, longitude, latitude]
      );

      if (result.affectedRows === 0) {
        return null;
      } else {
        // Update user's location flag
        await pool.query(
          'UPDATE users SET customer_set_location = ?, updated_at = ? WHERE cognito_id = ? AND username = ?',
          [customer_set_location, new Date(), cognito_id, username]
        );
      }

      return this.findByUsername(username);
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to update user location');
    }
  }

  static async findByCognitoId(cognitoId) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE cognito_id = ?',
        [cognitoId]
      );
      return rows.length ? rows[0] : null;
    } catch (error) {
      console.error('Database error:', error);
      throw new Error('Failed to fetch user');
    }
  }

  // // Additional method to verify user ownership
  // static async verifyUserOwnership(username, cognitoId) {
  //   try {
  //     const [rows] = await pool.query(
  //       'SELECT cognito_id FROM users WHERE username = ? AND cognito_id = ?',
  //       [username, cognitoId]
  //     );
  //     return rows.length > 0;
  //   } catch (error) {
  //     console.error('Database error:', error);
  //     throw new Error('Failed to verify user ownership');
  //   }
  // }

  // // Method to get user permissions based on user type
  // static getUserPermissions(userType) {
  //   const permissions = {
  //     customer: {
  //       canViewOwnProfile: true,
  //       canViewOtherProfiles: false,
  //       canUpdateOwnProfile: true,
  //       canUpdateOtherProfiles: false,
  //       canDeleteOwnProfile: true,
  //       canDeleteOtherProfiles: false,
  //       canListAllUsers: false,
  //       canAccessSensitiveData: false
  //     },
  //     nurse: {
  //       canViewOwnProfile: true,
  //       canViewOtherProfiles: true,
  //       canUpdateOwnProfile: true,
  //       canUpdateOtherProfiles: false, // Nurses can view but not update other profiles
  //       canDeleteOwnProfile: true,
  //       canDeleteOtherProfiles: false,
  //       canListAllUsers: true,
  //       canAccessSensitiveData: true
  //     }
  //   };

  //   return permissions[userType] || permissions.customer;
  // }
}

module.exports = UserService;