const AWS = require('aws-sdk');
const dotenv = require('dotenv');
const jwksRsa = require('jwks-rsa');

dotenv.config();

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// Initialize Cognito Identity Provider
const cognitoIdentityServiceProvider = new AWS.CognitoIdentityServiceProvider();

// JWT verification setup
const cognitoIssuer = `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}`;
const jwksClient = jwksRsa({
  jwksUri: `${cognitoIssuer}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true
});

module.exports = {
  cognitoIdentityServiceProvider,
  jwksClient,
  cognitoIssuer
}; 