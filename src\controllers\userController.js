const UserService = require('../services/userService');
const AuthService = require('../services/authService');

class UserController {
  static async getProfile(req, res) {
    try {
      // Use authenticated user's username from middleware
      const authenticatedUser = req.user;
      
      // Ensure user can only access their own profile
      const user = await UserService.findByUsername(authenticatedUser.email);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Verify the user's cognito_id matches the authenticated user
      if (user.cognito_id !== authenticatedUser.sub) {
        return res.status(403).json({ error: 'Access denied: Cannot access other user profiles' });
      }

      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone_number: user.phone_number,
        created_at: user.created_at,
        updated_at: user.updated_at,
        is_confirmed: user.is_confirmed,
        // Include location data if available
        latitude: user.latitude,
        longitude: user.longitude,
        address: user.address,
        customer_set_location: user.customer_set_location
      };

      res.json(userResponse);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      res.status(500).json({ error: 'Failed to fetch user profile' });
    }
  }

  static async updateProfile(req, res) {
    try {
      const authenticatedUser = req.user;
      const { given_name, family_name, phone_number } = req.body;

      // Validate phone number format
      if (phone_number && !phone_number.match(/^\+[1-9]\d{1,14}$/)) {
        return res.status(400).json({ error: 'Phone number must be in E.164 format (e.g., +12345678900)' });
      }

      // Verify user exists and matches authenticated user
      const existingUser = await UserService.findByUsername(authenticatedUser.username);
      if (!existingUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      if (existingUser.cognito_id !== authenticatedUser.sub) {
        return res.status(403).json({ error: 'Access denied: Cannot update other user profiles' });
      }

      // Update user in database with authenticated user's username
      const updatedUser = await UserService.updateProfile(
        authenticatedUser.username,
        authenticatedUser.sub,
        {
          given_name,
          family_name,
          phone_number
        }
      );

      if (!updatedUser) {
        return res.status(404).json({ error: 'Failed to update user profile' });
      }

      // Update user attributes in Cognito
      const cognitoAttributes = {};
      if (given_name) cognitoAttributes.given_name = given_name;
      if (family_name) cognitoAttributes.family_name = family_name;
      if (phone_number) cognitoAttributes.phone_number = phone_number;

      if (Object.keys(cognitoAttributes).length > 0) {
        await AuthService.updateUserAttributes(authenticatedUser.email, cognitoAttributes);
      }

      res.json(updatedUser);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ error: 'Failed to update user profile' });
    }
  }

  static async deleteProfile(req, res) {
    try {
      const authenticatedUser = req.user;

      // Verify user exists and matches authenticated user
      const existingUser = await UserService.findByUsername(authenticatedUser.username);
      if (!existingUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      if (existingUser.cognito_id !== authenticatedUser.sub) {
        return res.status(403).json({ error: 'Access denied: Cannot delete other user profiles' });
      }

      // Delete user from Cognito
      await AuthService.deleteUser(authenticatedUser.email);

      // Delete user from database using authenticated user's username
      const deleted = await UserService.deleteUser(authenticatedUser.username);
      if (!deleted) {
        return res.status(404).json({ error: 'User not found' });
      }

      res.json({ message: 'User deleted successfully' });
    } catch (error) {
      console.error('Error deleting user:', error);
      res.status(500).json({ error: 'Failed to delete user' });
    }
  }

  static async listUsers(req, res) {
    try {
      const authenticatedUser = req.user;
      
      // Add user type filtering based on authenticated user's permissions
      const queryParams = { 
        ...req.query,
        // You can add additional filters based on user type
        requestingUserType: authenticatedUser.userType,
        requestingUserId: authenticatedUser.sub
      };

      const result = await UserService.listUsers(queryParams);
      res.json(result);
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }

  static async getUserById(req, res) {
    try {
      const authenticatedUser = req.user;
      const { userId } = req.params;

      // Find user by ID
      const user = await UserService.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Authorization check: Users can only view their own profile unless they're a nurse
      if (authenticatedUser.userType !== 'nurse' && user.cognito_id !== authenticatedUser.sub) {
        return res.status(403).json({ 
          error: 'Access denied: You can only view your own profile' 
        });
      }

      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        phone_number: user.phone_number,
        created_at: user.created_at,
        updated_at: user.updated_at,
        is_confirmed: user.is_confirmed,
        // Include location data if available
        latitude: user.latitude,
        longitude: user.longitude,
        address: user.address,
      };

      res.json(userResponse);
    } catch (error) {
      console.error('Error fetching user:', error);
      res.status(500).json({ error: 'Failed to fetch user details' });
    }
  }

  static async updateLocation(req, res) {
    try {
      const authenticatedUser = req.user;
      const { latitude, longitude, address } = req.body;
      
      // Verify user exists and matches authenticated user
      const user = await UserService.findByCognitoId(authenticatedUser.sub);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Additional authorization check
      if (user.cognito_id !== authenticatedUser.sub) {
        return res.status(403).json({ error: 'Access denied: Cannot update other user locations' });
      }

      // Validate latitude and longitude if provided
      if (latitude && (isNaN(parseFloat(latitude)) || parseFloat(latitude) < -90 || parseFloat(latitude) > 90)) {
        return res.status(400).json({ error: 'Latitude must be a number between -90 and 90' });
      }
      if (longitude && (isNaN(parseFloat(longitude)) || parseFloat(longitude) < -180 || parseFloat(longitude) > 180)) {
        return res.status(400).json({ error: 'Longitude must be a number between -180 and 180' });
      }

      // Update user location in database
      const updatedUser = await UserService.updateLocation(
        authenticatedUser.email,
        authenticatedUser.sub,
        user.given_name,
        {
          latitude: latitude ? parseFloat(latitude) : null,
          longitude: longitude ? parseFloat(longitude) : null,
          address,
        }
      );

      if (!updatedUser) {
        return res.status(404).json({ error: 'Failed to update user location' });
      }
      res.json({
        message: "Location updated successfully",
        user: {
          username: updatedUser.username,
          latitude: updatedUser.latitude,
          longitude: updatedUser.longitude,
          address: updatedUser.address,
          given_name: updatedUser.given_name,
          cognito_id: updatedUser.cognito_id,
        },
      });
    } catch (error) {
      console.error('Error updating user location:', error);
      res.status(500).json({ error: 'Failed to update user location' });
    }
  }
}

module.exports = UserController;