const express = require('express');
const router = express.Router();
const ServiceStatusController = require('../controllers/ServiceStatusController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);
router.use(checkDatabaseConnection);

// Get service status by booking ID
router.get('/booking/:bookingId', ServiceStatusController.getServiceStatus);

// Update service status
router.put('/booking/:bookingId', ServiceStatusController.updateServiceStatus);

// Add this route for populating accepted bookings
router.post('/populate-accepted', ServiceStatusController.populateAcceptedBookings);

// Get all service statuses for a nurse
router.get('/nurse/:nurseCognitoId', ServiceStatusController.getNurseServiceStatuses);

// Get all service statuses for a customer
router.get('/customer/:customerCognitoId', ServiceStatusController.getCustomerServiceStatuses);

module.exports = router;