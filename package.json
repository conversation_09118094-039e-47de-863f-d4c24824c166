{"name": "customer-api", "version": "1.0.0", "description": "Node.js API with AWS Cognito authentication and MySQL database", "main": "src/server.js", "scripts": {"start": "nodemon src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"aws-sdk": "^2.1692.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^2.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.6.5", "redis": "^4.6.11", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.9"}}