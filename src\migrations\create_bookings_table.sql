-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  booking_id BIGINT NOT NULL UNIQUE,
  nurse_cognitoId VARCHAR(255) NOT NULL,
  customer_cognitoId VARCHAR(255) NOT NULL,
  nurse_given_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  customer_given_name VA<PERSON>HAR(255) NOT NULL,
  nurse_location_latitude DECIMAL(10, 8) NULL,
  nurse_location_longitude DECIMAL(11, 8) NULL,
  nurse_location_address TEXT NULL,
  customer_booked_location_address TEXT NOT NULL,
  customer_booked_location_latitude DECIMAL(10, 8) NOT NULL,
  customer_booked_location_longitude DECIMAL(11, 8) NOT NULL,
  hourly_fare DECIMAL(10, 2) NOT NULL,
  availability_date DATE NOT NULL,
  availability_slot VARCHAR(100) NOT NULL,
  booking_status ENUM('Pending', 'Accepted', 'Declined', 'Cancelled', 'Completed') DEFAULT 'Pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for better performance
  INDEX idx_booking_id (booking_id),
  INDEX idx_nurse_cognitoId (nurse_cognitoId),
  INDEX idx_customer_cognitoId (customer_cognitoId),
  INDEX idx_booking_status (booking_status),
  INDEX idx_availability_date (availability_date),
  INDEX idx_nurse_location (nurse_location_latitude, nurse_location_longitude),
  INDEX idx_customer_location (customer_booked_location_latitude, customer_booked_location_longitude)
); 