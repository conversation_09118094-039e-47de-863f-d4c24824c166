const AWS = require('aws-sdk');
const { cognitoIdentityServiceProvider } = require('../config/aws');
const UserService = require('./userService');

class AuthService {
  static async register({ username, password, email, given_name, family_name, middle_name, phone_number }) {
    try {
      // Check if user already exists in database
      const existingUser = await UserService.findByUsername(username);
      if (existingUser) {
        throw new Error('Username already exists in database');
      }

      const existingPhoneNumber = await UserService.findByPhoneNumber(phone_number);
      if (existingPhoneNumber) {
        throw new Error('Phone number already exists in database');
      }
      const existingEmail = await UserService.findByEmail(email);
      if (existingEmail) {
        throw new Error('Email already exists in database');
      }

      // Set up parameters for Cognito user registration
      const params = {
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        Username: email,
        Password: password,
        UserAttributes: [
          {
            Name: 'email',
            Value: email
          },
          {
            Name: 'preferred_username',
            Value: username
          }
        ]
      };

      // Add optional attributes if provided
      if (given_name) {
        params.UserAttributes.push({
          Name: 'given_name',
          Value: given_name
        });
      }

      if (family_name) {
        params.UserAttributes.push({
          Name: 'family_name',
          Value: family_name
        });
      }

      if (phone_number) {
        params.UserAttributes.push({
          Name: 'phone_number',
          Value: phone_number
        });
      }

      // Call Cognito to register the user
      const cognitoResult = await cognitoIdentityServiceProvider.signUp(params).promise();

      // Insert user into database
      const dbUser = await UserService.create({
        cognito_id: cognitoResult.UserSub,
        username,
        email,
        given_name,
        family_name,
        middle_name,
        phone_number,
        is_confirmed: cognitoResult.UserConfirmed
      });

      return {
        success: true
      };
    } catch (error) {
      console.error('Error registering user:', error);
      throw error;
    }
  }

  static async confirmRegistration(username, confirmationCode) {
    try {
      const params = {
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        Username: username,
        ConfirmationCode: confirmationCode
      };

      await cognitoIdentityServiceProvider.confirmSignUp(params).promise();

      // Update user confirmation status in database
      await UserService.updateConfirmationStatus(username, true);

      return {
        message: 'User confirmed successfully'
      };
    } catch (error) {
      console.error('Error confirming user:', error);
      throw error;
    }
  }

  static async loginWithPhoneNumber(phoneNumber, password) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if user is confirmed
      if (!user.is_confirmed) {
        throw new Error('User is not confirmed');
      }
      // Authenticate with Cognito using the email associated with the phone number
      // Note: Make sure ALLOW_USER_PASSWORD_AUTH is enabled in your Cognito app client settings
      const params = {
        AuthFlow: 'USER_PASSWORD_AUTH',
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        AuthParameters: {
          USERNAME: user.email,
          PASSWORD: password
        }
      };

      const authResult = await cognitoIdentityServiceProvider.initiateAuth(params).promise();
      // Check if AuthenticationResult exists
      if (!authResult || !authResult.AuthenticationResult) {
        console.error('Authentication failed. No AuthenticationResult in response:', authResult);
        throw new Error('Authentication failed. No authentication result returned.');
      }

      // Return the authentication tokens and user info
      return {
        tokens: {
          idToken: authResult.AuthenticationResult.IdToken,
          accessToken: authResult.AuthenticationResult.AccessToken,
          refreshToken: authResult.AuthenticationResult.RefreshToken,
          expiresIn: authResult.AuthenticationResult.ExpiresIn
        },
        user: {
          id: user.id,
          cognito_id: user.cognito_id,
          username: user.username,
          email: user.email,
          phone_number: user.phone_number,
          given_name: user.given_name,
          family_name: user.family_name,
          patient_profile_complete: user.patient_profile_complete,
          patient_documents_complete: user.patient_documents_complete,
          address: user.address,
          latitude: user.latitude,
          longitude: user.longitude,
        }
      };
    } catch (error) {
      console.error('Error logging in with phone number:', error);
      throw error;
    }
  }

  static async updateUserAttributes(username, attributes) {
    try {
      const cognitoParams = {
        UserPoolId: process.env.CUSTOMER_COGNITO_USER_POOL_ID,
        Username: username,
        UserAttributes: Object.entries(attributes).map(([key, value]) => ({
          Name: key,
          Value: value
        }))
      };

      await cognitoIdentityServiceProvider.adminUpdateUserAttributes(cognitoParams).promise();
      return true;
    } catch (error) {
      console.error('Error updating user attributes:', error);
      throw error;
    }
  }

  static async deleteUser(username) {
    try {
      const cognitoParams = {
        UserPoolId: process.env.CUSTOMER_COGNITO_USER_POOL_ID,
        Username: username
      };
      await cognitoIdentityServiceProvider.adminDeleteUser(cognitoParams).promise();
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  static async forgotPassword(phoneNumber) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }

      const params = {
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        Username: user.email // Cognito uses email as username
      };

      await cognitoIdentityServiceProvider.forgotPassword(params).promise();
      return true;
    } catch (error) {
      console.error('Error in forgot password:', error);
      if (error.code === 'UserNotFoundException') {
        throw new Error('User not found');
      } else if (error.code === 'InvalidParameterException') {
        throw new Error('Invalid phone number format');
      }
      throw error;
    }
  }

  static async confirmForgotPassword(phoneNumber, confirmationCode, newPassword) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }

      const params = {
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        Username: user.email, // Cognito uses email as username
        ConfirmationCode: confirmationCode,
        Password: newPassword
      };

      await cognitoIdentityServiceProvider.confirmForgotPassword(params).promise();
      return true;
    } catch (error) {
      console.error('Error in confirm forgot password:', error);
      if (error.code === 'UserNotFoundException') {
        throw new Error('User not found');
      } else if (error.code === 'InvalidParameterException') {
        throw new Error('Invalid confirmation code or password format');
      } else if (error.code === 'CodeMismatchException') {
        throw new Error('Invalid confirmation code');
      }
      throw error;
    }
  }

  static async resendOTP(phoneNumber) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }

      // First, invalidate any existing OTP by attempting to confirm with a dummy code
      try {
        const invalidateParams = {
          ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
          Username: user.email,
          ConfirmationCode: '000000' // Dummy code to invalidate existing OTP
        };
        await cognitoIdentityServiceProvider.confirmSignUp(invalidateParams).promise();
      } catch (error) {
        // Ignore the error as we expect it to fail with CodeMismatchException
        if (error.code !== 'CodeMismatchException') {
          throw error;
        }
      }

      // Now resend the confirmation code
      const params = {
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        Username: user.email // Cognito uses email as username
      };

      await cognitoIdentityServiceProvider.resendConfirmationCode(params).promise();
      return true;
    } catch (error) {
      console.error('Error in resend OTP:', error);
      if (error.code === 'UserNotFoundException') {
        throw new Error('User not found');
      } else if (error.code === 'InvalidParameterException') {
        throw new Error('Invalid phone number format');
      } else if (error.code === 'LimitExceededException') {
        throw new Error('Too many attempts. Please try again later');
      } else if (error.code === 'UserNotConfirmedException') {
        throw new Error('User is already confirmed');
      }
      throw error;
    }
  }

  static async changePassword(phoneNumber, oldPassword, newPassword) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }

      // First authenticate the user with old password
      const authParams = {
        AuthFlow: 'USER_PASSWORD_AUTH',
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        AuthParameters: {
          USERNAME: user.email,
          PASSWORD: oldPassword
        }
      };

      const authResult = await cognitoIdentityServiceProvider.initiateAuth(authParams).promise();

      if (!authResult || !authResult.AuthenticationResult) {
        throw new Error('Invalid old password');
      }

      // Change password using the access token
      const changePasswordParams = {
        AccessToken: authResult.AuthenticationResult.AccessToken,
        PreviousPassword: oldPassword,
        ProposedPassword: newPassword
      };

      await cognitoIdentityServiceProvider.changePassword(changePasswordParams).promise();
      return true;
    } catch (error) {
      console.error('Error in change password:', error);
      if (error.code === 'UserNotFoundException') {
        throw new Error('User not found');
      } else if (error.code === 'NotAuthorizedException') {
        throw new Error('Invalid old password');
      } else if (error.code === 'InvalidPasswordException') {
        throw new Error('New password does not meet requirements');
      } else if (error.code === 'LimitExceededException') {
        throw new Error('Too many attempts. Please try again later');
      }
      throw error;
    }
  }

  static async deleteAccount(phoneNumber, password) {
    try {
      // Find user by phone number in the database
      const user = await UserService.findByPhoneNumber(phoneNumber);
      if (!user) {
        throw new Error('User not found');
      }
      // First authenticate the user
      const authParams = {
        AuthFlow: 'USER_PASSWORD_AUTH',
        ClientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
        AuthParameters: {
          USERNAME: user.email,
          PASSWORD: password
        }
      };

      const authResult = await cognitoIdentityServiceProvider.initiateAuth(authParams).promise();

      if (!authResult || !authResult.AuthenticationResult) {
        throw new Error('Invalid password');
      }
      // Delete user from Cognito
      const deleteParams = {
        UserPoolId: process.env.CUSTOMER_COGNITO_USER_POOL_ID,
        Username: user.email
      };

      await cognitoIdentityServiceProvider.adminDeleteUser(deleteParams).promise();
      // Delete user from database
      await UserService.deleteUser(user.username);
      return true;
    } catch (error) {
      console.error('Error in delete account:', error);
      if (error.code === 'UserNotFoundException') {
        throw new Error('User not found');
      } else if (error.code === 'NotAuthorizedException') {
        throw new Error('Invalid password');
      } else if (error.code === 'LimitExceededException') {
        throw new Error('Too many attempts. Please try again later');
      }
      throw error;
    }
  }
}

module.exports = AuthService;