const { pool } = require("../config/database");

class Address {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.name = data.name;
    this.address = data.address;
    this.icon = data.icon;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.longitude = data.longitude;
    this.latitude = data.latitude;
  }

  // Create new address
  static async create(addressData) {
    try {
      // Validate required fields
      if (!addressData.user_id) {
        throw new Error("User ID is required");
      }
      
      if (!addressData.name || addressData.name.trim() === '') {
        throw new Error("Address name is required");
      }

      const [result] = await pool.execute(
        `INSERT INTO addresses 
         (user_id, name, address, icon, created_at, updated_at, longitude, latitude) 
         VALUES (?, ?, ?, ?, CONVERT_TZ(NOW(), 'UTC', '+05:30'), CONVERT_TZ(NOW(), 'UTC', '+05:30'), ?, ?)`,
        [
          addressData.user_id,
          addressData.name.trim(),
          addressData.address || null,
          addressData.icon || 'other',
          addressData.longitude || null,
          addressData.latitude || null,
        ]
      );

      console.log(`Address created with ID: ${result.insertId} for user: ${addressData.user_id}`);
      
      // Return the created address
      return await Address.findById(result.insertId);
    } catch (error) {
      console.error("Model error - create:", error);
      throw new Error(`Failed to create address: ${error.message}`);
    }
  }

  // Update address with user context
  static async update(addressData) {
    try {
      // Validate required fields
      if (!addressData.id) {
        throw new Error("Address ID is required");
      }
      
      if (!addressData.user_id) {
        throw new Error("User ID is required");
      }

      // Build dynamic update query based on provided fields
      const updateFields = [];
      const updateValues = [];

      if (addressData.name !== undefined) {
        updateFields.push("name = ?");
        updateValues.push(addressData.name.trim());
      }
      
      if (addressData.address !== undefined) {
        updateFields.push("address = ?");
        updateValues.push(addressData.address || null);
      }
      
      if (addressData.longitude !== undefined) {
        updateFields.push("longitude = ?");
        updateValues.push(addressData.longitude || null);
      }
      
      if (addressData.latitude !== undefined) {
        updateFields.push("latitude = ?");
        updateValues.push(addressData.latitude || null);
      }
      
      if (addressData.icon !== undefined) {
        updateFields.push("icon = ?");
        updateValues.push(addressData.icon || 'other');
      }

      // Always update the timestamp
      updateFields.push("updated_at = CONVERT_TZ(NOW(), 'UTC', '+05:30')");

      // Add WHERE clause parameters
      updateValues.push(addressData.id);
      updateValues.push(addressData.user_id);

      const query = `UPDATE addresses SET ${updateFields.join(", ")} WHERE id = ? AND user_id = ?`;

      const [result] = await pool.execute(query, updateValues);

      if (result.affectedRows === 0) {
        throw new Error("Address not found or you don't have permission to update it");
      }

      console.log(`Address ${addressData.id} updated for user: ${addressData.user_id}`);
      
      // Return the updated address
      return await Address.findById(addressData.id);
    } catch (error) {
      console.error("Model error - update:", error);
      throw new Error(`Failed to update address: ${error.message}`);
    }
  }

  // Find addresses by user ID (user can only see their own addresses)
  static async findByUserId(user_id) {
    try {
      if (!user_id) {
        throw new Error("User ID is required");
      }

      const [rows] = await pool.execute(
        `SELECT * FROM addresses 
         WHERE user_id = ? 
         ORDER BY created_at DESC`,
        [user_id]
      );

      console.log(`Found ${rows.length} addresses for user: ${user_id}`);
      return rows.map(row => new Address(row));
    } catch (error) {
      console.error("Model error - findByUserId:", error);
      throw new Error(`Failed to find addresses: ${error.message}`);
    }
  }

  // Find single address by ID
  static async findById(id) {
    try {
      if (!id) {
        throw new Error("Address ID is required");
      }

      const [rows] = await pool.execute(
        "SELECT * FROM addresses WHERE id = ?",
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return new Address(rows[0]);
    } catch (error) {
      console.error("Model error - findById:", error);
      throw new Error(`Failed to find address: ${error.message}`);
    }
  }

  // Delete address with user context
  static async delete(id, user_id = null) {
    try {
      if (!id) {
        throw new Error("Address ID is required");
      }

      let query = "DELETE FROM addresses WHERE id = ?";
      let params = [id];

      // If user_id is provided, ensure user can only delete their own addresses
      if (user_id) {
        query += " AND user_id = ?";
        params.push(user_id);
      }

      const [result] = await pool.execute(query, params);

      const success = result.affectedRows > 0;
      
      if (success) {
        console.log(`Address ${id} deleted ${user_id ? `for user: ${user_id}` : ''}`);
      } else {
        console.log(`Address ${id} not found or user doesn't have permission to delete it`);
      }

      return success;
    } catch (error) {
      console.error("Model error - delete:", error);
      throw new Error(`Failed to delete address: ${error.message}`);
    }
  }

  // Check if address exists
  static async exists(id) {
    try {
      if (!id) {
        throw new Error("Address ID is required");
      }

      const [rows] = await pool.execute(
        "SELECT COUNT(*) as count FROM addresses WHERE id = ?",
        [id]
      );

      return rows[0].count > 0;
    } catch (error) {
      console.error("Model error - exists:", error);
      throw new Error(`Failed to check address existence: ${error.message}`);
    }
  }

  // Get all addresses with optional search (admin function - use with caution)
  static async findAll(searchTerm = "", userId = null) {
    try {
      let query = "SELECT * FROM addresses";
      let params = [];

      if (userId && searchTerm) {
        query += " WHERE user_id = ? AND (name LIKE ? OR address LIKE ?)";
        const searchPattern = `%${searchTerm}%`;
        params = [userId, searchPattern, searchPattern];
      } else if (userId) {
        query += " WHERE user_id = ?";
        params = [userId];
      } else if (searchTerm) {
        // This should be used with caution as it searches across all users
        query += " WHERE name LIKE ? OR address LIKE ?";
        const searchPattern = `%${searchTerm}%`;
        params = [searchPattern, searchPattern];
      }

      query += " ORDER BY created_at DESC";

      const [rows] = await pool.execute(query, params);
      return rows.map((row) => new Address(row));
    } catch (error) {
      console.error("Model error - findAll:", error);
      throw new Error(`Failed to fetch addresses: ${error.message}`);
    }
  }
}

module.exports = Address;