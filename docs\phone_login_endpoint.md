# Phone Number Login Endpoint

This document describes the endpoint for logging in with a phone number and password.

## Endpoint

`POST /api/auth/login/phone`

## Request Body

The request body should be a JSON object with the following properties:

| Field       | Type   | Required | Description                                      |
|-------------|--------|----------|--------------------------------------------------|
| phone_number| String | Yes      | Phone number in E.164 format (e.g., +12345678900)|
| password    | String | Yes      | User's password                                  |

## Example Request

```json
{
  "phone_number": "+12345678900",
  "password": "yourpassword"
}
```

## Response

### Success Response

**Code:** 200 OK

**Content:**

```json
{
  "tokens": {
    "idToken": "eyJraWQiOiJxdWxRc...",
    "accessToken": "eyJraWQiOiJxd...",
    "refreshToken": "eyJjdHkiOiJKV...",
    "expiresIn": 3600
  },
  "user": {
    "id": 123,
    "username": "johndoe",
    "email": "<EMAIL>",
    "phone_number": "+12345678900",
    "given_name": "John",
    "family_name": "Doe"
  }
}
```

### Error Responses

**Validation Error:**

**Code:** 400 Bad Request

**Content:**

```json
{
  "errors": [
    {
      "value": "1234567890",
      "msg": "Phone number must be in E.164 format (e.g., +12345678900)",
      "param": "phone_number",
      "location": "body"
    }
  ]
}
```

**User Not Found:**

**Code:** 404 Not Found

**Content:**

```json
{
  "error": "User not found"
}
```

**Incorrect Password:**

**Code:** 401 Unauthorized

**Content:**

```json
{
  "error": "Incorrect username or password"
}
```

**User Not Confirmed:**

**Code:** 403 Forbidden

**Content:**

```json
{
  "error": "User is not confirmed"
}
```

**Server Error:**

**Code:** 500 Internal Server Error

**Content:**

```json
{
  "error": "Server error. Please try again later."
}
```

## Notes

- The phone number must be in E.164 format (e.g., +12345678900)
- The user must be confirmed before they can log in
- The endpoint returns both authentication tokens and basic user information
- The tokens can be used to authenticate subsequent API requests
- The ID token should be included in the Authorization header as a Bearer token
