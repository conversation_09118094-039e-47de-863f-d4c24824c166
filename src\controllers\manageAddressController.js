const addressService = require("../services/manageAddressService");

class AddressController {
  // POST /api/addresses/create - Create new address
  async createAddress(req, res) {
    try {
      // Get authenticated user info
      const userId = req.user.sub;
      const userType = req.user.userType;
      const addressData = req.body;

      console.log(`Creating address for ${userType} user: ${userId}`);

      const result = await addressService.createAddress(userId, addressData);

      res.status(201).json({
        message: "Address created successfully",
        result,
      });
    } catch (error) {
      console.error("Controller error - createAddress:", error);
      
      // Handle specific error types
      if (error.message.includes('validation')) {
        return res.status(400).json({ 
          success: false,
          error: "Validation failed",
          details: error.message 
        });
      }
      
      res.status(500).json({ 
        success: false,
        error: "Failed to create address" 
      });
    }
  }

  // PUT /api/addresses/update/:id - Update existing address
  async updateAddress(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.sub;
      const userType = req.user.userType;
      const addressData = req.body;

      console.log(`Updating address ${id} for ${userType} user: ${userId}`);

      // The ensureOwnership middleware has already verified the user owns this address
      const result = await addressService.updateAddress(id, addressData, userId);

      res.status(200).json({
        message: "Address updated successfully",
        result,
      });
    } catch (error) {
      console.error("Controller error - updateAddress:", error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({ 
          success: false,
          error: "Address not found" 
        });
      }
      
      if (error.message.includes('validation')) {
        return res.status(400).json({ 
          success: false,
          error: "Validation failed",
          details: error.message 
        });
      }
      
      res.status(500).json({ 
        success: false,
        error: "Failed to update address" 
      });
    }
  }

  // DELETE /api/addresses/:id - Delete address
  async deleteAddress(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.sub;
      const userType = req.user.userType;

      console.log(`Deleting address ${id} for ${userType} user: ${userId}`);

      // The ensureOwnership middleware has already verified the user owns this address
      const result = await addressService.deleteAddress(id, userId);

      res.status(200).json({
        message: "Address deleted successfully",
        result,
      });
    } catch (error) {
      console.error("Controller error - deleteAddress:", error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({ 
          success: false,
          error: "Address not found" 
        });
      }
      
      res.status(500).json({ 
        success: false,
        error: "Failed to delete address" 
      });
    }
  }

  // GET /api/addresses/search - Get all addresses for authenticated user
  async getAllAddresses(req, res) {
    try {
      const userId = req.user.sub;
      const userType = req.user.userType;

      console.log(`Fetching all addresses for ${userType} user: ${userId}`);

      const result = await addressService.getAllAddressesById(userId);

      res.status(200).json({
        message: "Addresses retrieved successfully",
        data: result,
      });
    } catch (error) {
      console.error("Controller error - getAllAddresses:", error);
      res.status(500).json({ 
        success: false,
        error: "Failed to retrieve addresses" 
      });
    }
  }

  // GET /api/addresses/:id - Get single address by ID
  async getAddressById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.sub;
      const userType = req.user.userType;

      console.log(`Fetching address ${id} for ${userType} user: ${userId}`);

      // The ensureOwnership middleware has already verified the user owns this address
      // and stored it in req.address, but we'll fetch it again for consistency
      const result = await addressService.getAddressById(id, userId);

      res.status(200).json({
        message: "Address retrieved successfully",
        data: result,
      });
    } catch (error) {
      console.error("Controller error - getAddressById:", error);
      
      if (error.message.includes('not found')) {
        return res.status(404).json({ 
          success: false,
          error: "Address not found" 
        });
      }
      
      res.status(500).json({ 
        success: false,
        error: "Failed to retrieve address" 
      });
    }
  }
}

module.exports = new AddressController();