# Nurserv API

A Node.js API with AWS Cognito authentication, MySQL database, and Redis caching.

## Features

- User registration and authentication using AWS Cognito
- JWT token validation and authorization
- MySQL database integration
- Redis caching for improved performance
- User profile management
- Advanced search and filtering capabilities
- Rate limiting and security headers
- API documentation

## Prerequisites

- Node.js (v14 or higher)
- MySQL (v8 or higher)
- Redis (v6 or higher)
- AWS Account with Cognito User Pool configured

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Server
PORT=8080

# AWS Cognito
AWS_REGION=your-region
COGNITO_USER_POOL_ID=your-user-pool-id
COGNITO_CLIENT_ID=your-client-id

# MySQL
DB_HOST=localhost
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=your-db-name

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/nurserv-api.git
cd nurserv-api
```

2. Install dependencies:
```bash
npm install
```

3. Set up the database:
```sql
CREATE DATABASE your_database_name;
USE your_database_name;

CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  cognito_id VARCHAR(255) NOT NULL UNIQUE,
  username VARCHAR(255) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  given_name VARCHAR(255),
  family_name VARCHAR(255),
  phone_number VARCHAR(20),
  is_confirmed BOOLEAN DEFAULT FALSE,
  latitude DECIMAL(10, 8) NULL,
  longitude DECIMAL(11, 8) NULL,
  address VARCHAR(255) NULL,
  city VARCHAR(100) NULL,
  state VARCHAR(100) NULL,
  country VARCHAR(100) NULL,
  postal_code VARCHAR(20) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

4. Run the migration script to add location fields to the users table:
```bash
node src/scripts/run_migration.js
```

5. Start Redis:
```bash
# macOS
brew services start redis

# Linux
sudo service redis-server start
```

## Running the Application

Development mode:
```bash
npm run dev
```

Production mode:
```bash
npm start
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/confirm` - Confirm user registration
- `POST /api/auth/login/phone` - Login with phone number and password

### Users

- `GET /api/users/profile` - Get current user's profile
- `PUT /api/users/profile` - Update user profile
- `DELETE /api/users/profile` - Delete user profile
- `POST /api/users/location` - Update user location details
- `GET /api/users` - List users with pagination, search, and filters
- `GET /api/users/:userId` - Get user by ID

### Documents

- `POST /api/documents/upload` - Upload a document
- `GET /api/documents` - List user documents
- `GET /api/documents/:id` - Get document by ID
- `PUT /api/documents/:id` - Update document metadata
- `GET /api/documents/s3/:s3Key/url` - Get a signed URL for a document
- `DELETE /api/documents/s3/:s3Key` - Delete a document

## Search and Filtering

The list users endpoint supports the following query parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)
- `search` - Search across multiple fields
- `email` - Filter by exact email match
- `username` - Filter by exact username match
- `is_confirmed` - Filter by confirmation status
- `sort_by` - Field to sort by (default: created_at)
- `sort_order` - Sort direction (ASC/DESC)
- `fields` - Comma-separated list of fields to return

## Security

- JWT token validation
- Rate limiting (100 requests per 15 minutes per IP)
- Security headers (Helmet)
- CORS enabled
- Input validation
- SQL injection prevention
- Redis caching for performance

## Testing

Run tests:
```bash
npm test
```

## License

MIT