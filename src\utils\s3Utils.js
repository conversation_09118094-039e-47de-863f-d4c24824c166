const AWS = require('aws-sdk');
require('dotenv').config();

// Configure AWS S3
const s3 = new AWS.S3({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const generateSignedUrl = async (s3Key) => {
  try {
    if (!s3Key) {
      throw new Error('S3 key is required');
    }

    // Use provided bucket name or fall back to environment variable or default
    const BUCKET_NAME = process.env.AWS_S3_PROFILE_BUCKET_NAME;

    const params = {
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Expires: 3600
    };

    const signedUrl = await s3.getSignedUrlPromise('getObject', params);
    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw new Error(`Failed to generate signed URL: ${error.message}`);
  }
};

module.exports = {
  generateSignedUrl,
  s3
};
