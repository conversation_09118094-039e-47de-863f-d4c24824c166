const Address = require("../models/manageAddressModel");

class AddressService {
  // Create new address with user context
  async createAddress(userId, addressData) {
    try {
      // Validate user ID
      if (!userId) {
        throw new Error("User ID is required");
      }

      // Validate required fields
      if (!addressData.name || addressData.name.trim() === '') {
        throw new Error("Address name is required");
      }

      // Add user_id to the addressData
      const addressWithUserId = {
        ...addressData,
        user_id: userId,
      };

      console.log(`Creating address for user: ${userId}`);
      return await Address.create(addressWithUserId);
    } catch (error) {
      console.error("Service error - createAddress:", error);
      throw error;
    }
  }

  // Update existing address with user context
  async updateAddress(id, addressData, userId) {
    try {
      // Validate inputs
      if (!id || isNaN(parseInt(id))) {
        throw new Error("Address ID must be a valid number");
      }
      
      if (!userId) {
        throw new Error("User ID is required");
      }

      // Check if address exists and belongs to user
      const existingAddress = await Address.findById(parseInt(id));
      if (!existingAddress) {
        throw new Error("Address with the specified ID does not exist");
      }

      // Verify ownership
      if (existingAddress.user_id !== userId) {
        throw new Error("Access denied: You can only update your own addresses");
      }

      // Validate at least one field is provided for update
      const { name, address, icon, latitude, longitude } = addressData;
      if (!name && !address && !icon && !latitude && !longitude) {
        throw new Error("At least one field must be provided for update");
      }

      // Prepare update data
      const updateData = {
        ...addressData,
        id: parseInt(id),
        user_id: userId // Ensure user_id is preserved
      };

      console.log(`Updating address ${id} for user: ${userId}`);
      return await Address.update(updateData);
    } catch (error) {
      console.error("Service error - updateAddress:", error);
      throw error;
    }
  }

  // Delete address with user context
  async deleteAddress(id, userId) {
    try {
      // Validate inputs
      if (!id || isNaN(parseInt(id))) {
        throw new Error("Address ID must be a valid number");
      }
      
      if (!userId) {
        throw new Error("User ID is required");
      }

      // Check if address exists and belongs to user
      const existingAddress = await Address.findById(parseInt(id));
      if (!existingAddress) {
        throw new Error("Address with the specified ID does not exist");
      }

      // Verify ownership
      if (existingAddress.user_id !== userId) {
        throw new Error("Access denied: You can only delete your own addresses");
      }

      console.log(`Deleting address ${id} for user: ${userId}`);
      const result = await Address.delete(parseInt(id));
      
      return {
        deleted: result,
        id: parseInt(id),
        message: result ? "Address deleted successfully" : "Address not found"
      };
    } catch (error) {
      console.error("Service error - deleteAddress:", error);
      throw error;
    }
  }

  // Get all addresses by user ID
  async getAllAddressesById(userId) {
    try {
      // Validate user ID
      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log(`Fetching all addresses for user: ${userId}`);
      const addresses = await Address.findByUserId(userId);
      
      // Return empty array if no addresses found - this is valid
      return addresses || [];
    } catch (error) {
      console.error("Service error - getAllAddressesById:", error);
      throw error;
    }
  }

  // Get single address by ID with user context
  async getAddressById(id, userId) {
    try {
      // Validate inputs
      if (!id || isNaN(parseInt(id))) {
        throw new Error("Address ID must be a valid number");
      }
      
      if (!userId) {
        throw new Error("User ID is required");
      }

      console.log(`Fetching address ${id} for user: ${userId}`);
      const address = await Address.findById(parseInt(id)); 
      
      if (!address) {
        throw new Error("Address with the specified ID does not exist");
      }

      // Verify ownership
      if (address.user_id !== userId) {
        throw new Error("Access denied: You can only access your own addresses");
      }

      return address;
    } catch (error) {
      console.error("Service error - getAddressById:", error);
      throw error;
    }
  }
}

module.exports = new AddressService();