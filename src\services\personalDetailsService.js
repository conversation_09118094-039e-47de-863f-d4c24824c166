const PersonalDetails = require('../models/personalDetailsModel');

class PersonalDetailsService {
  
  /**
   * Validate user access to resource
   */
  static validateUserAccess(userId, userType) {
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    if (!userType || (userType !== 'customer' && userType !== 'nurse')) {
      throw new Error('Invalid user type');
    }
  }

  /**
   * Upsert personal details with user validation
   */
  static async upsertPersonalDetails(userId, detailsData, userType) {
    try {
      // Validate user access
      this.validateUserAccess(userId, userType);

      // Check if personal details already exist for this user
      const existingDetails = await PersonalDetails.findByUserId(userId);
      
      // Add user metadata to the details data
      const detailsWithMetadata = {
        ...detailsData,
        cognito_id: userId,
        user_type: userType, // Store user type for reference
      };
      
      if (existingDetails) {
        // If details exist, update them
        return await PersonalDetails.personalDetailsUpdate(userId, detailsWithMetadata, userType);
      } else {
        // If details don't exist, create new ones
        return await PersonalDetails.create(detailsWithMetadata, userType);
      }
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  /**
   * Get personal details with user validation
   */
  static async getPersonalDetails(userId, userType) {
    try {
      // Validate user access
      this.validateUserAccess(userId, userType);

      const details = await PersonalDetails.findByUserId(userId);
      
      if (!details) {
        throw new Error('Personal details not found');
      }

      // Additional validation: ensure user can only access their own data
      if (details.cognito_id !== userId) {
        throw new Error('Unauthorized access');
      }

      return details;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  /**
   * Update personal details with user validation
   */
  static async updatePersonalDetails(userId, updateData, userType) {
    try {
      // Validate user access
      this.validateUserAccess(userId, userType);

      // Ensure user can only update their own data
      const existingDetails = await PersonalDetails.findByUserId(userId);
      if (existingDetails && existingDetails.cognito_id !== userId) {
        throw new Error('Unauthorized access');
      }

      // Add metadata for audit trail
      const updateDataWithMetadata = {
        ...updateData,
        modified_at: new Date()
      };

      const updatedPersonalDetails = await PersonalDetails.personalDetailsUpdate(
        userId, 
        updateDataWithMetadata, 
        userType
      );
      
      if (!updatedPersonalDetails) {
        throw new Error('Personal details not found');
      }
      
      return updatedPersonalDetails;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }

  /**
   * Delete personal details with user validation
   */
  static async deletePersonalDetails(userId, userType) {
    try {
      // Validate user access
      this.validateUserAccess(userId, userType);

      // Ensure user can only delete their own data
      const existingDetails = await PersonalDetails.findByUserId(userId);
      if (existingDetails && existingDetails.cognito_id !== userId) {
        throw new Error('Unauthorized access');
      }

      const deleted = await PersonalDetails.delete(userId, userType);
      
      if (!deleted) {
        throw new Error('Personal details not found');
      }   
      return { message: 'Personal details deleted successfully' };
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}

module.exports = PersonalDetailsService;