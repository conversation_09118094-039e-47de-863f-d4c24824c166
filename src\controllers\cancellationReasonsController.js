const cancellationReasonsService = require('../services/cancellationReasonsService');

class CancellationReasonsController {
  // GET /api/cancellation-reasons - Get all cancellation reasons
  static async getAllCancellationReasons(req, res) {
    try {
      const reasons = await cancellationReasonsService.getAllCancellationReasons();
      return res.status(200).json({
        success: true,
        data: reasons
      });
    } catch (error) {
      console.error('Error fetching cancellation reasons:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch cancellation reasons',
        error: error.message
      });
    }
  }
};

module.exports = CancellationReasonsController;