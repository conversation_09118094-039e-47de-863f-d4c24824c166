# User Location Endpoint

This document describes the endpoint for updating user location details.

## Endpoint

`POST /api/users/location`

## Authentication

This endpoint requires authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Request Body

The request body should be a JSON object with the following properties:

| Field       | Type    | Required | Description                                      |
|-------------|---------|----------|--------------------------------------------------|
| latitude    | Number  | No       | Latitude coordinate (-90 to 90)                  |
| longitude   | Number  | No       | Longitude coordinate (-180 to 180)               |
| address     | String  | No       | Street address                                   |
| city        | String  | No       | City name                                        |
| state       | String  | No       | State or province                                |
| country     | String  | No       | Country name                                     |
| postal_code | String  | No       | Postal or ZIP code                               |

## Example Request

```json
{
  "latitude": 37.7749,
  "longitude": -122.4194,
  "address": "123 Main St",
  "city": "San Francisco",
  "state": "CA",
  "country": "USA",
  "postal_code": "94105"
}
```

## Response

### Success Response

**Code:** 200 OK

**Content:**

```json
{
  "message": "Location updated successfully",
  "user": {
    "username": "johndoe",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "address": "123 Main St",
    "city": "San Francisco",
    "state": "CA",
    "country": "USA",
    "postal_code": "94105"
  }
}
```

### Error Responses

**User Not Found:**

**Code:** 404 Not Found

**Content:**

```json
{
  "error": "User not found"
}
```

**Invalid Latitude/Longitude:**

**Code:** 400 Bad Request

**Content:**

```json
{
  "error": "Latitude must be a number between -90 and 90"
}
```

or

```json
{
  "error": "Longitude must be a number between -180 and 180"
}
```

**Server Error:**

**Code:** 500 Internal Server Error

**Content:**

```json
{
  "error": "Failed to update user location"
}
```

## Notes

- All fields are optional, but at least one field should be provided
- The endpoint will update only the fields that are provided in the request
- Existing location data will be preserved for fields not included in the request
- Latitude and longitude values are validated to ensure they are within valid ranges
