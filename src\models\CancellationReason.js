const { pool } = require('../config/database');

class CancellationReason {
  // Get all cancellation reasons
  static async findAll() {
    try {
      const [rows] = await pool.execute(
        'SELECT * FROM cancellation_reasons WHERE isActive = 1',
        []
      );
      return rows;
    } catch (error) {
      console.error('Error in findAll:', error);
      throw new Error(`Error fetching cancellation reasons: ${error.message}`);
    }
  }
}

module.exports = CancellationReason;