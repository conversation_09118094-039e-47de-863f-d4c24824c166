const express = require('express');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const PersonalDetailsController = require('../controllers/personalDetailsController');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);

// Create personal details
router.post('/create', PersonalDetailsController.createPersonalDetails);

// Get personal details
router.get('/getProfile', PersonalDetailsController.getPersonalDetails);

// Update personal details
router.put('/updatePersonalProfile', PersonalDetailsController.updatePersonalDetails);

// Delete personal details
router.delete('/deleteProfile', PersonalDetailsController.deletePersonalDetails);

module.exports = router; 