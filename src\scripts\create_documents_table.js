const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
  // Create MySQL connection
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME
  });

  try {
    console.log('Connected to database');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../migrations/create_documents_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL statements
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (const statement of statements) {
      console.log(`Executing: ${statement}`);
      await connection.query(statement);
    }
    
    console.log('Documents table created successfully');
  } catch (error) {
    console.error('Error creating documents table:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

runMigration();
