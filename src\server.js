require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const jwksRsa = require('jwks-rsa');
const multer = require('multer');
const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
const { authRoutes, userRoutes, healthRoutes, proximityRoutes, bookingRoutes } = require('./routes');
const personalDetailsRoutes = require('./routes/personalDetails');
const addressRoutes = require('./routes/manageAddress');
const serviceStatusRoutes = require('./routes/serviceStatusRoutes');
const cancellationReasonsRoutes = require('./routes/cancellationReasonsRoutes');

const app = express();

app.set('trust proxy', true);
// Middleware`
app.use(helmet()); // Security headers
app.use(compression()); // Compress responses
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://localhost:3004',
    'http://localhost:3005',
    'http://localhost:3006',
    'http://localhost:3007',
    'http://localhost:3008',
    'http://localhost:8000',
    'http://localhost:8001',
    'http://localhost:8002',
    'http://localhost:8003',
    'http://localhost:8004',
    'http://localhost:8005',
    'http://localhost:8006',
    'http://localhost:8007',
    'http://localhost:8008',
    'https://testcustomerapp.nurserv.com',
    'https://testcustomerapi.nurserv.com',
    'https://testnurservapp.nurserv.com',
    'https://testnurservapi.nurserv.com',
    'https://nurseapp.nurserv.com',
    'https://customerapp.nurserv.com'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['Content-Type', 'Authorization'],
  preflightContinue: false,
  optionsSuccessStatus: 204,
  maxAge: 86400,
})); // Enable CORS
app.use(express.json()); // Parse JSON bodies
app.use(morgan('dev')); // Logging

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/personal-details', personalDetailsRoutes);
app.use('/api/addresses', addressRoutes);
app.use('/health', healthRoutes);
app.use('/api/proximity', proximityRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/service-status', serviceStatusRoutes);
app.use('/api/cancellation-reasons', cancellationReasonsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});
app.get("/test", (req, res) =>{
  res.json({message: "Welcome Nurser serve API."})
})

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log('Redis client connected');
});