const ServiceStatusService = require('../services/ServiceStatusService');

class ServiceStatusController {
  // Get service status by booking ID
  static async getServiceStatus(req, res) {
    try {
      const { bookingId } = req.params;
      const userId = req.user?.sub;

      if (!bookingId) {
        return res.status(400).json({
          success: false,
          message: 'Booking ID is required',
          data: null
        });
      }

      // Validate bookingId is a valid number
      if (isNaN(bookingId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid booking ID format',
          data: null
        });
      }

      const serviceStatus = await ServiceStatusService.getService(bookingId, userId);

      res.status(200).json({
        success: true,
        message: 'Service status retrieved successfully',
        data: serviceStatus
      });
    } catch (error) {
      console.error('Error in getServiceStatus controller:', error);
      const statusCode = error.message.includes('not found') ? 404 :
        error.message.includes('Unauthorized') ? 403 : 500;

      res.status(statusCode).json({
        success: false,
        message: error.message,
        data: null
      });
    }
  }

  // Update service status - only nurses can update
  static async updateServiceStatus(req, res) {
    try {
      const { bookingId } = req.params;
      const { status } = req.body;
      const userId = req.user?.sub;
      const userType = req.user?.userType;

      if (!bookingId || !status) {
        return res.status(400).json({
          success: false,
          message: 'Booking ID and status are required',
          data: null
        });
      }

      // Validate bookingId is a valid number
      if (isNaN(bookingId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid booking ID format',
          data: null
        });
      }

      // Additional check to ensure only nurses can update
      if (userType !== 'nurse') {
        return res.status(403).json({
          success: false,
          message: 'Access denied: Only nurses can update service status',
          data: null
        });
      }

      const result = await ServiceStatusService.updateService(bookingId, status, userId);
      res.status(200).json(result);
    } catch (error) {
      console.error('Error in updateServiceStatus controller:', error);

      const statusCode = error.message.includes('Unauthorized') ? 403 :
        error.message.includes('not found') ? 404 :
          error.message.includes('Invalid') ? 400 : 500;

      res.status(statusCode).json({
        success: false,
        message: error.message,
        data: null
      });
    }
  }

  // Get all service statuses for a nurse
  static async getNurseServiceStatuses(req, res) {
    try {
      const { nurseCognitoId } = req.params;
      const requestingUserId = req.user?.sub;
      const userType = req.user?.userType;

      // Double-check authorization (already handled in routes, but good practice)
      if (userType !== 'nurse' || requestingUserId !== nurseCognitoId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You can only access your own service statuses',
          data: null
        });
      }

      const serviceStatuses = await ServiceStatusService.getNurseService(nurseCognitoId);

      res.status(200).json({
        success: true,
        message: 'Nurse service statuses retrieved successfully',
        data: serviceStatuses
      });
    } catch (error) {
      console.error('Error in getNurseServiceStatuses controller:', error);
      res.status(500).json({
        success: false,
        message: error.message,
        data: null
      });
    }
  }

  // Get all service statuses for a customer
  static async getCustomerServiceStatuses(req, res) {
    try {
      const { customerCognitoId } = req.params;
      const requestingUserId = req.user?.sub;
      const userType = req.user?.userType;

      // Double-check authorization (already handled in routes, but good practice)
      if (userType !== 'customer' || requestingUserId !== customerCognitoId) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: You can only access your own service statuses',
          data: null
        });
      }

      const serviceStatuses = await ServiceStatusService.getCustomerService(customerCognitoId);

      res.status(200).json({
        success: true,
        message: 'Customer service statuses retrieved successfully',
        data: serviceStatuses
      });
    } catch (error) {
      console.error('Error in getCustomerServiceStatuses controller:', error);
      res.status(500).json({
        success: false,
        message: error.message,
        data: null
      });
    }
  }

  // Populate service status for accepted bookings (admin function)
  static async populateAcceptedBookings(req, res) {
    console.log('[populateAcceptedBookings] Request received:', {
      body: req.body,
      params: req.params,
      query: req.query,
      user: req.user
    });

    try {
      const result = await ServiceStatusService.populateAcceptedBookings();
      console.log('[populateAcceptedBookings] Result:', result);
      res.status(200).json(result);
    } catch (error) {
      console.error('[populateAcceptedBookings] Error:', error);
      res.status(500).json({
        success: false,
        message: error.message,
        data: null
      });
    }
  }
}

module.exports = ServiceStatusController;