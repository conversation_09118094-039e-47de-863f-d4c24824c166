const express = require('express');
const router = express.Router();
const cancellationReasonsController = require('../controllers/cancellationReasonsController');
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');

const requireNurseOrCustomer = (req, res, next) => {
  if (req.user && (req.user.userType === 'nurse' || req.user.userType === 'customer')) {
    return next();
  }
  return res.status(403).json({ 
    error: 'Access denied: Nurse or Customer access required',
    userType: req.user ? req.user.userType : 'unknown'
  });
};

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);
router.use(checkDatabaseConnection);

// GET /api/cancellation-reasons
router.get('/',requireNurseOrCustomer, cancellationReasonsController.getAllCancellationReasons);

module.exports = router;