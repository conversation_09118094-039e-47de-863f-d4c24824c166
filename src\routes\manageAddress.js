const express = require("express");
const { authenticateToken, requireUserType } = require('../middleware/auth');
const checkDatabaseConnection = require('../middleware/database');
const addressController = require("../controllers/manageAddressController");
const { body, param, query, validationResult } = require("express-validator");

const router = express.Router();

// Validation rules for creating new address
const createAddressValidation = [
  body("name")
    .notEmpty()
    .withMessage("Name is required")
    .isLength({ min: 1 })
    .withMessage("Name must be at least 1 character long")
    .isLength({ max: 100 })
    .withMessage("Name must not exceed 100 characters"),

  body("address")
    .optional({ nullable: true, checkFalsy: false })
    .isLength({ max: 1000 })
    .withMessage("Address must not exceed 1000 characters"),
  
  body("latitude")
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage("Latitude must be between -90 and 90"),

  body("longitude").optional().isFloat({ min: -180, max: 180 }).withMessage("Longitude must be between -180 and 180"),

  body("icon")
    .optional()
    .isIn(["home", "other"])
    .withMessage("Icon must be one of: home, other")
    .default("other"),
];

// Validation rules for updating address (all fields optional)
const updateAddressValidation = [
  body("name")
    .optional()
    .notEmpty()
    .withMessage("Name cannot be empty if provided")
    .isLength({ min: 1 })
    .withMessage("Name must be at least 1 character long")
    .isLength({ max: 100 })
    .withMessage("Name must not exceed 100 characters"),

  body("address")
    .optional({ nullable: true, checkFalsy: false })
    .isLength({ max: 1000 })
    .withMessage("Address must not exceed 1000 characters"),

  body("latitude")
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage("Latitude must be between -90 and 90"),

  body("longitude").optional().isFloat({ min: -180, max: 180 }).withMessage("Longitude must be between -180 and 180"),

  body("icon")
    .optional()
    .isIn(["home", "other"])
    .withMessage("Icon must be one of: home, other"),

  // Custom validator to ensure at least one field is provided for update
  body().custom((value, { req }) => {
    const { name, address, icon } = req.body;
    if (!name && !address && !icon) {
      throw new Error("At least one field must be provided for update");
    }
    return true;
  }),
];

// ID validation (for route parameters)
const validateId = [
  param("id").isInt({ min: 1 }).withMessage("ID must be a positive integer"),
];

// Middleware to handle validation results
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors: errors.array(),
    });
  }
  next();
};

// All routes require authentication
router.use(authenticateToken);
router.use(requireUserType);
router.use(checkDatabaseConnection);

// POST /api/addresses/create
router.post("/create", createAddressValidation, handleValidationErrors, addressController.createAddress);

// GET /api/addresses/search - Get all addresses
router.get("/search", handleValidationErrors, addressController.getAllAddresses);

// PUT /api/addresses/update/:id - Update address
router.put("/update/:id", validateId, updateAddressValidation, handleValidationErrors, addressController.updateAddress);

// DELETE /api/addresses/:id - Delete address
router.delete("/:id", validateId, handleValidationErrors, addressController.deleteAddress);

// // GET /api/addresses/:id - Get single address by ID
// router.get("/:id", validateId, handleValidationErrors, addressController.getAddressById);

module.exports = router;